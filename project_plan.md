# Project Plan for Engineering Structural Tools

## Core Architectural Principles

1.  **Modularity & Decoupling:** Each design tool should be as independent as possible. This minimizes ripple effects when one tool changes and makes development, testing, and deployment easier.
2.  **Plugin-Oriented Architecture (POA):** The core system should know how to discover and load plugins, but not their specific implementations. Plugins register themselves with the core.
3.  **Clear Contracts (APIs):** Define clear interfaces for how plugins interact with the core system and potentially with each other (though IFC will be the primary inter-tool data exchange).
4.  **Scalability:** The architecture should be able to accommodate new tools without significant refactoring of existing code.
5.  **Maintainability:** Easy to understand, debug, and update.
6.  **User Experience:** Despite being a plugin system, the user should perceive it as a unified application.

## Application Structure

### 1\. The Core Application (Host)

This is the main executable that launches and manages the plugins.

  * **Main Application Window (PyQt):**
      * **Menu Bar:** Dynamically populated by plugins. Each plugin registers its main entry point (e.g., "Footing Design," "Wind Load Analysis").
      * **Central Widget Area:** Use `QStackedWidget` or a similar layout that allows displaying different plugin UIs when selected from the menu.
      * **Status Bar:** For application-wide messages.
  * **Plugin Manager/Loader:**
      * **Discovery Mechanism:**
          * **Entry Points (Poetry):** Plugins declare themselves in their `pyproject.toml` as entry points for a specific group (e.g., `pps.tools`). The core application then uses `importlib.metadata` to discover and load these entry points.
      * **Loading Logic:** Responsible for importing plugin modules and instantiating their main classes.
      * **Lifecycle Management:** Handles plugin initialization, shutdown, and error handling.
  * **Configuration Manager:**
      * Loads application-wide settings (e.g., paths to IFC libraries, default units).
      * Could also manage plugin-specific settings.
      * Use `QSettings` for user-specific settings for application-wide defaults.
  * **Logger:** A centralized logging facility to collect messages from the core and all plugins. Use Python's standard `logging` module.

### 2\. Plugin Structure (Modular Design Tool)

Each modular design tool is a self-contained unit.

  * **Plugin Base Class/Interface (Abstract Base Class - ABC):**
      * Define an `ABC` (from `abc` module) in your core application that all plugins *must* inherit from. This enforces a common interface.
      * **Required Methods:**
          * `initialize(self, host_api)`: Called when the plugin is loaded. `host_api` is an object providing access to core services (e.g., logging, UI registration).
          * `get_menu_items(self) -> List[Tuple[str, Callable]]`: Returns a list of menu item names and the callable functions/methods to execute when selected.
          * `get_tool_name(self) -> str`: Returns a human-readable name for the tool.
          * `get_tool_icon(self) -> QIcon` (optional): For menu icons.
          * `run_tool(self, parent_widget: QWidget)`: This method is called when the user selects the tool from the menu. It should create and display the tool's UI within the provided `parent_widget`.
  * **UI Implementation (PyQt):**
      * Each plugin will have its own `QWidget` or `QDialog` subclass for its user interface.
      * **Input Fields:** `QLineEdit`, `QDoubleSpinBox`, `QComboBox`, `QTableWidget` etc.
      * **Output Area:** `QTextEdit`, `QGraphicsView` for graphical output. You might consider using Matplotlib or Plotly for plotting within `QGraphicsView`.
      * **Event Handling:** Connect UI elements to internal logic.
  * **Business Logic/Core Calculations:**
      * Separated from the UI. This makes it easier to test independently and potentially reuse in a non-GUI context.
      * This is where your structural engineering formulas and algorithms reside.
  * **Data Handling (IFC):**
      * **IFC Reader/Writer:** Each plugin will need to use an IFC library (e.g., `ifcopenshell`, `ifc-python`) to read and write data.
      * **Data Model:** Internally, plugins might have their own Pythonic data models that map to and from IFC entities. This prevents direct coupling to the IFC library's internal representation.
  * **Tests:** Unit tests for business logic, and potentially integration tests for the UI.
  * **`pyproject.toml` (for Poetry):**
      * Declares plugin dependencies.
      * Crucially, defines the `[tool.poetry.plugins.pps_tools]` (or similar) entry point.

### 3\. Shared Components / Libraries

These are libraries that might be used by multiple plugins or the core.

  * **IFC Wrapper/Helper Library:**
      * A thin wrapper around `ifcopenshell` (or your chosen IFC library) to provide common functionalities or simplify its API for your specific use cases.
      * Standardize IFC schema versions used.
  * **Common UI Widgets/Utilities:** If you find yourself repeatedly creating similar input widgets or validation logic, abstract them into a shared UI library.
  * **Units Management:** A consistent way to handle units (e.g., using `pint` library) across all tools is critical for engineering applications.
  * **Reporting/Plotting Utilities:** Common functions for generating text reports or plots.

### 4\. Data Exchange via IFC

  * **IFC as the Single Source of Truth:** All inter-tool data exchange happens by reading from or writing to IFC files. This minimizes direct coupling between plugins.
  * **Workflow:**
    1.  User starts with a base IFC model (if available for current project – if not create new IFC file for project).
    2.  User runs "Footing Design" tool. It reads necessary data from the IFC, performs calculations, and *updates/adds* footing information back into the same (or a new) IFC file.
    3.  User runs "Steel Connections" tool. It reads steel member information (potentially including footings) from the *updated* IFC, designs connections, and writes connection details back to the IFC.
  * **Challenges with IFC:**
      * **Granularity:** IFC is very detailed. You'll need to decide which entities and properties are relevant for each tool.
      * **Versioning:** Be clear about the IFC schema version (e.g., IFC4) you will support.
      * **Partial Updates:** How do you update an IFC file without overwriting unrelated data? `ifcopenshell` allows for this.
      * **Mapping:** Establishing clear mappings between your internal Pythonic data models and IFC entities will be crucial. Consider creating a standardized data model layer above `ifcopenshell` within your shared components.

## Development Workflow & Deployment

      * **Core App:** Your main `pyproject.toml` for the core app, with `pyqt6`, `ifcopenshell` etc. as dependencies.
      * **Plugins:** Each plugin can be its own Poetry project during development. This is excellent for isolating dependencies during initial creation and testing.
      * **Monorepo:** One Git repository containing the core app and all plugins as subdirectories. Simplifies dependency management if plugins share many dependencies.

## UI/UX Considerations

  * **Consistency:** Maintain a consistent look and feel across all plugin UIs. Define a style guide or use Qt Style Sheets.
  * **Feedback:** Provide clear feedback to the user (e.g., progress bars for calculations, status messages).
  * **Error Handling:** Graceful error handling and informative error messages.
  * **Accessibility:** Consider users with different needs.

## Technology Stack Summary

  * **Core Language:** Python 3.9+
  * **GUI Framework:** PyQt6 (or PySide6)
  * **Dependency Management:** Poetry
  * **Version Control:** Git
  * **Data Exchange:** IFC (via `ifcopenshell`)
  * **Executable Packaging:** PyInstaller / cx\_Freeze (at later stage)
