# Babel configuration file for extracting translatable strings
# This file defines the extraction rules for different file types

[python: **.py]
# Extract strings from Python files
# Look for _(), ngettext(), pgettext() function calls

[jinja2: **/templates/**.html]
# Extract strings from Jinja2 templates (if used in future)

[jinja2: **/templates/**.xml]
# Extract strings from XML templates

# Ignore certain directories and files
[ignore: **/tests/**]
[ignore: **/build/**]
[ignore: **/dist/**]
[ignore: **/__pycache__/**]
[ignore: **/.*]
