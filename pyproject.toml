[project]
name = "eng-struct-tools"
version = "0.1.0"
description = "Engineering Structural Analysis and Design Tools - A modular plugin-based application"
authors = [
    {name = "<PERSON>", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "pyqt6>=6.6.0",
    "ifcopenshell>=0.7.0",
    "pint>=0.23.0",
    "numpy>=1.24.0",
    "matplotlib>=3.7.0",
    "pydantic>=2.5.0",
    "typing-extensions>=4.8.0",
    "babel>=2.14.0",
    "polib>=1.2.0"
]

[project.scripts]
eng-struct-tools = "eng_struct_tools.core_app.main:main"

[project.entry-points."eng_struct_tools.plugins"]
footing_design = "eng_struct_tools.plugins.footing_design:FootingDesignPlugin"

[tool.poetry]
name = "eng-struct-tools"
version = "0.1.0"
description = "Engineering Structural Analysis and Design Tools - A modular plugin-based application"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "eng_struct_tools", from = "src"}]

[tool.poetry.dependencies]
python = "^3.12"
pyqt6 = "^6.6.0"
ifcopenshell = "^0.7.0"
pint = "^0.23.0"
numpy = "^1.24.0"
matplotlib = "^3.7.0"
pydantic = "^2.5.0"
typing-extensions = "^4.8.0"
babel = "^2.14.0"
polib = "^1.2.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.4.1"
pytest-qt = "^4.2.0"
pytest-cov = "^4.1.0"
black = "^23.12.0"
isort = "^5.13.0"
mypy = "^1.8.0"
flake8 = "^7.0.0"

[tool.poetry.scripts]
eng-struct-tools = "eng_struct_tools.core_app.main:main"

[tool.poetry.plugins."eng_struct_tools.plugins"]
footing_design = "eng_struct_tools.plugins.footing_design:FootingDesignPlugin"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "--cov=src/eng_struct_tools --cov-report=html --cov-report=term-missing"
